// 游戏常量
const GAME_CONFIG = {
    CANVAS_WIDTH: 800,
    CANVAS_HEIGHT: 600,
    GRID_SIZE: 40,
    FPS: 60,
    
    // 游戏平衡参数
    INITIAL_COINS: 100,
    INITIAL_LIVES: 10,
    COIN_GENERATION_RATE: 1000, // 每秒生成金币
    
    // 床铺位置（基地）
    BED_POSITION: { x: 400, y: 500 },
    BED_SIZE: { width: 120, height: 80 },
    
    // 窗户位置（敌人入口）
    WINDOWS: [
        { x: 50, y: 100 },
        { x: 750, y: 100 },
        { x: 50, y: 300 },
        { x: 750, y: 300 }
    ]
};

// 防御塔配置
const TOWER_CONFIG = {
    desk: {
        name: '书桌',
        cost: 50,
        damage: 25,
        range: 100,
        fireRate: 1000,
        icon: '📚',
        color: '#8B4513'
    },
    chair: {
        name: '椅子',
        cost: 30,
        damage: 15,
        range: 80,
        fireRate: 800,
        icon: '🪑',
        color: '#654321'
    },
    lamp: {
        name: '台灯',
        cost: 80,
        damage: 40,
        range: 120,
        fireRate: 1200,
        icon: '💡',
        color: '#FFD700'
    },
    fan: {
        name: '风扇',
        cost: 120,
        damage: 60,
        range: 150,
        fireRate: 1500,
        icon: '🌀',
        color: '#87CEEB'
    }
};

// 敌人配置
const ENEMY_CONFIG = {
    ghost: {
        name: '小鬼',
        health: 50,
        speed: 1,
        reward: 10,
        color: '#9370DB'
    },
    demon: {
        name: '恶魔',
        health: 100,
        speed: 0.8,
        reward: 20,
        color: '#DC143C'
    },
    spirit: {
        name: '幽灵',
        health: 80,
        speed: 1.5,
        reward: 15,
        color: '#00CED1'
    },
    nightmare: {
        name: '梦魇',
        health: 200,
        speed: 0.6,
        reward: 50,
        color: '#8B0000'
    }
};

// 工具函数
const Utils = {
    // 计算两点距离
    distance(p1, p2) {
        const dx = p1.x - p2.x;
        const dy = p1.y - p2.y;
        return Math.sqrt(dx * dx + dy * dy);
    },
    
    // 计算角度
    angle(from, to) {
        return Math.atan2(to.y - from.y, to.x - from.x);
    },
    
    // 检查点是否在矩形内
    pointInRect(point, rect) {
        return point.x >= rect.x && 
               point.x <= rect.x + rect.width &&
               point.y >= rect.y && 
               point.y <= rect.y + rect.height;
    },
    
    // 检查是否可以放置防御塔
    canPlaceTower(x, y, towers) {
        const gridX = Math.floor(x / GAME_CONFIG.GRID_SIZE) * GAME_CONFIG.GRID_SIZE;
        const gridY = Math.floor(y / GAME_CONFIG.GRID_SIZE) * GAME_CONFIG.GRID_SIZE;
        
        // 检查是否在床铺区域
        const bedArea = {
            x: GAME_CONFIG.BED_POSITION.x - GAME_CONFIG.BED_SIZE.width / 2,
            y: GAME_CONFIG.BED_POSITION.y - GAME_CONFIG.BED_SIZE.height / 2,
            width: GAME_CONFIG.BED_SIZE.width,
            height: GAME_CONFIG.BED_SIZE.height
        };
        
        if (this.pointInRect({ x: gridX, y: gridY }, bedArea)) {
            return false;
        }
        
        // 检查是否在窗户区域
        for (const window of GAME_CONFIG.WINDOWS) {
            if (this.distance({ x: gridX, y: gridY }, window) < 50) {
                return false;
            }
        }
        
        // 检查是否已有防御塔
        for (const tower of towers) {
            if (tower.x === gridX && tower.y === gridY) {
                return false;
            }
        }
        
        return true;
    },
    
    // 随机选择数组元素
    randomChoice(array) {
        return array[Math.floor(Math.random() * array.length)];
    },
    
    // 格式化数字显示
    formatNumber(num) {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toString();
    }
};