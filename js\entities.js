// 防御塔类
class Tower {
    constructor(x, y, type) {
        this.x = x;
        this.y = y;
        this.type = type;
        this.config = TOWER_CONFIG[type];
        this.level = 1;
        this.lastFire = 0;
        this.target = null;
        this.size = GAME_CONFIG.GRID_SIZE - 4;
    }
    
    update(enemies, currentTime) {
        // 寻找目标
        this.findTarget(enemies);
        
        // 攻击目标
        if (this.target && currentTime - this.lastFire >= this.config.fireRate) {
            this.fire(currentTime);
        }
    }
    
    findTarget(enemies) {
        this.target = null;
        let closestDistance = this.config.range;
        
        for (const enemy of enemies) {
            const distance = Utils.distance(this, enemy);
            if (distance <= this.config.range && distance < closestDistance) {
                this.target = enemy;
                closestDistance = distance;
            }
        }
    }
    
    fire(currentTime) {
        if (this.target) {
            // 创建子弹
            const bullet = new Bullet(
                this.x + this.size / 2,
                this.y + this.size / 2,
                this.target,
                this.config.damage * this.level
            );
            
            game.bullets.push(bullet);
            this.lastFire = currentTime;
        }
    }
    
    draw(ctx) {
        // 绘制防御塔
        ctx.fillStyle = this.config.color;
        ctx.fillRect(this.x, this.y, this.size, this.size);
        
        // 绘制图标
        ctx.font = '20px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(
            this.config.icon,
            this.x + this.size / 2,
            this.y + this.size / 2 + 7
        );
        
        // 绘制等级
        if (this.level > 1) {
            ctx.fillStyle = '#FFD700';
            ctx.font = '12px Arial';
            ctx.fillText(
                this.level.toString(),
                this.x + this.size - 8,
                this.y + 12
            );
        }
        
        // 绘制射程（如果被选中）
        if (game.selectedTower === this) {
            ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.arc(
                this.x + this.size / 2,
                this.y + this.size / 2,
                this.config.range,
                0,
                Math.PI * 2
            );
            ctx.stroke();
        }
    }
    
    upgrade() {
        this.level++;
        return this.config.cost * this.level;
    }
}

// 敌人类
class Enemy {
    constructor(type, path) {
        this.type = type;
        this.config = ENEMY_CONFIG[type];
        this.health = this.config.health;
        this.maxHealth = this.config.health;
        this.speed = this.config.speed;
        this.path = path;
        this.pathIndex = 0;
        this.x = path[0].x;
        this.y = path[0].y;
        this.size = 20;
        this.alive = true;
    }
    
    update() {
        if (!this.alive || this.pathIndex >= this.path.length - 1) {
            return;
        }
        
        const target = this.path[this.pathIndex + 1];
        const dx = target.x - this.x;
        const dy = target.y - this.y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        
        if (distance < 5) {
            this.pathIndex++;
            if (this.pathIndex >= this.path.length - 1) {
                // 到达床铺，扣除生命值
                game.lives--;
                this.alive = false;
            }
        } else {
            this.x += (dx / distance) * this.speed;
            this.y += (dy / distance) * this.speed;
        }
    }
    
    takeDamage(damage) {
        this.health -= damage;
        if (this.health <= 0) {
            this.alive = false;
            game.coins += this.config.reward;
            game.score += this.config.reward * 10;
        }
    }
    
    draw(ctx) {
        if (!this.alive) return;
        
        // 绘制敌人
        ctx.fillStyle = this.config.color;
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.size / 2, 0, Math.PI * 2);
        ctx.fill();
        
        // 绘制血条
        const barWidth = this.size;
        const barHeight = 4;
        const healthPercent = this.health / this.maxHealth;
        
        ctx.fillStyle = '#FF0000';
        ctx.fillRect(
            this.x - barWidth / 2,
            this.y - this.size / 2 - 8,
            barWidth,
            barHeight
        );
        
        ctx.fillStyle = '#00FF00';
        ctx.fillRect(
            this.x - barWidth / 2,
            this.y - this.size / 2 - 8,
            barWidth * healthPercent,
            barHeight
        );
    }
}

// 子弹类
class Bullet {
    constructor(x, y, target, damage) {
        this.x = x;
        this.y = y;
        this.target = target;
        this.damage = damage;
        this.speed = 5;
        this.size = 4;
        this.alive = true;
    }
    
    update() {
        if (!this.target || !this.target.alive) {
            this.alive = false;
            return;
        }
        
        const dx = this.target.x - this.x;
        const dy = this.target.y - this.y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        
        if (distance < this.size) {
            // 击中目标
            this.target.takeDamage(this.damage);
            this.alive = false;
        } else {
            this.x += (dx / distance) * this.speed;
            this.y += (dy / distance) * this.speed;
        }
    }
    
    draw(ctx) {
        if (!this.alive) return;
        
        ctx.fillStyle = '#FFFF00';
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.size / 2, 0, Math.PI * 2);
        ctx.fill();
    }
}

// 波次管理器
class WaveManager {
    constructor() {
        this.currentWave = 1;
        this.enemiesInWave = 0;
        this.enemiesSpawned = 0;
        this.waveStartTime = 0;
        this.spawnInterval = 1000;
        this.lastSpawn = 0;
        this.waveActive = false;
    }
    
    startWave() {
        this.waveActive = true;
        this.enemiesInWave = 5 + this.currentWave * 3;
        this.enemiesSpawned = 0;
        this.waveStartTime = Date.now();
        this.spawnInterval = Math.max(500, 1500 - this.currentWave * 50);
    }
    
    update(currentTime) {
        if (!this.waveActive) return;
        
        if (this.enemiesSpawned < this.enemiesInWave && 
            currentTime - this.lastSpawn >= this.spawnInterval) {
            this.spawnEnemy();
            this.lastSpawn = currentTime;
        }
        
        // 检查波次是否结束
        if (this.enemiesSpawned >= this.enemiesInWave && 
            game.enemies.filter(e => e.alive).length === 0) {
            this.endWave();
        }
    }
    
    spawnEnemy() {
        const enemyTypes = Object.keys(ENEMY_CONFIG);
        let enemyType;
        
        // 根据波次选择敌人类型
        if (this.currentWave < 3) {
            enemyType = 'ghost';
        } else if (this.currentWave < 6) {
            enemyType = Utils.randomChoice(['ghost', 'demon']);
        } else if (this.currentWave < 10) {
            enemyType = Utils.randomChoice(['ghost', 'demon', 'spirit']);
        } else {
            enemyType = Utils.randomChoice(enemyTypes);
        }
        
        // 随机选择窗户作为起点
        const startWindow = Utils.randomChoice(GAME_CONFIG.WINDOWS);
        const path = this.generatePath(startWindow);
        
        const enemy = new Enemy(enemyType, path);
        game.enemies.push(enemy);
        this.enemiesSpawned++;
    }
    
    generatePath(startWindow) {
        const bedPos = GAME_CONFIG.BED_POSITION;
        return [
            startWindow,
            { x: startWindow.x + (bedPos.x - startWindow.x) * 0.3, y: startWindow.y + (bedPos.y - startWindow.y) * 0.3 },
            { x: startWindow.x + (bedPos.x - startWindow.x) * 0.7, y: startWindow.y + (bedPos.y - startWindow.y) * 0.7 },
            bedPos
        ];
    }
    
    endWave() {
        this.waveActive = false;
        this.currentWave++;
        game.coins += 20 + this.currentWave * 5; // 波次奖励
        
        // 短暂延迟后开始下一波
        setTimeout(() => {
            if (game.gameState === 'playing') {
                this.startWave();
            }
        }, 3000);
    }
}