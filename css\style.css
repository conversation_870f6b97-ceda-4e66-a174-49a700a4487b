* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #1a1a2e, #16213e);
    color: #fff;
    overflow: hidden;
}

#gameContainer {
    display: flex;
    flex-direction: column;
    height: 100vh;
    max-width: 1200px;
    margin: 0 auto;
}

#gameHeader {
    background: rgba(0, 0, 0, 0.8);
    padding: 10px 20px;
    border-bottom: 2px solid #ff6b6b;
}

.stats {
    display: flex;
    justify-content: space-around;
    align-items: center;
}

.stat {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 18px;
    font-weight: bold;
}

.label {
    color: #ffd93d;
}

#gameCanvas {
    background: #2c3e50;
    border: 3px solid #34495e;
    display: block;
    margin: 0 auto;
    cursor: crosshair;
}

#gameControls {
    background: rgba(0, 0, 0, 0.9);
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 2px solid #ff6b6b;
}

.tower-shop h3 {
    color: #ffd93d;
    margin-bottom: 10px;
    text-align: center;
}

.tower-buttons {
    display: flex;
    gap: 10px;
}

.tower-btn {
    background: linear-gradient(145deg, #3a4a5c, #2c3e50);
    border: 2px solid #34495e;
    border-radius: 10px;
    padding: 10px;
    color: #fff;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 80px;
    text-align: center;
}

.tower-btn:hover {
    background: linear-gradient(145deg, #4a5a6c, #3c4e60);
    border-color: #ff6b6b;
    transform: translateY(-2px);
}

.tower-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.tower-btn.selected {
    border-color: #ffd93d;
    background: linear-gradient(145deg, #5a6a7c, #4c5e70);
}

.tower-icon {
    font-size: 24px;
    margin-bottom: 5px;
}

.tower-name {
    font-size: 12px;
    font-weight: bold;
    margin-bottom: 3px;
}

.tower-cost {
    font-size: 10px;
    color: #ffd93d;
}

.game-actions {
    display: flex;
    gap: 10px;
}

.action-btn {
    background: linear-gradient(145deg, #ff6b6b, #e55555);
    border: none;
    border-radius: 8px;
    padding: 12px 20px;
    color: #fff;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.action-btn:hover {
    background: linear-gradient(145deg, #e55555, #d44444);
    transform: translateY(-2px);
}

.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal.hidden {
    display: none;
}

.modal-content {
    background: linear-gradient(145deg, #2c3e50, #34495e);
    padding: 30px;
    border-radius: 15px;
    text-align: center;
    border: 3px solid #ff6b6b;
    min-width: 300px;
}

.modal-content h2 {
    color: #ff6b6b;
    margin-bottom: 20px;
    font-size: 28px;
}

.modal-content p {
    margin: 10px 0;
    font-size: 18px;
}

@media (max-width: 768px) {
    #gameCanvas {
        width: 100%;
        height: auto;
    }
    
    .tower-buttons {
        flex-wrap: wrap;
    }
    
    #gameControls {
        flex-direction: column;
        gap: 15px;
    }
    
    .stats {
        flex-wrap: wrap;
        gap: 10px;
    }
}