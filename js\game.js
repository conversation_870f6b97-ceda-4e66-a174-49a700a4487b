// 主游戏类
class Game {
    constructor() {
        this.canvas = document.getElementById('gameCanvas');
        this.ctx = this.canvas.getContext('2d');
        
        // 游戏状态
        this.gameState = 'menu'; // menu, playing, paused, gameOver
        this.coins = GAME_CONFIG.INITIAL_COINS;
        this.lives = GAME_CONFIG.INITIAL_LIVES;
        this.score = 0;
        
        // 游戏对象
        this.towers = [];
        this.enemies = [];
        this.bullets = [];
        this.waveManager = new WaveManager();
        
        // 交互状态
        this.selectedTowerType = null;
        this.selectedTower = null;
        this.lastCoinGeneration = Date.now();
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.gameLoop();
    }
    
    setupEventListeners() {
        // 画布点击事件
        this.canvas.addEventListener('click', (e) => this.handleCanvasClick(e));
        this.canvas.addEventListener('mousemove', (e) => this.handleMouseMove(e));
        
        // 防御塔选择按钮
        document.querySelectorAll('.tower-btn').forEach(btn => {
            btn.addEventListener('click', (e) => this.selectTowerType(e));
        });
        
        // 游戏控制按钮
        document.getElementById('startBtn').addEventListener('click', () => this.startGame());
        document.getElementById('pauseBtn').addEventListener('click', () => this.pauseGame());
        document.getElementById('restartBtn').addEventListener('click', () => this.restartGame());
        document.getElementById('restartGameBtn').addEventListener('click', () => this.restartGame());
    }
    
    handleCanvasClick(e) {
        if (this.gameState !== 'playing') return;
        
        const rect = this.canvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        
        if (this.selectedTowerType) {
            this.placeTower(x, y);
        } else {
            this.selectTower(x, y);
        }
    }
    
    handleMouseMove(e) {
        if (this.gameState !== 'playing' || !this.selectedTowerType) return;
        
        const rect = this.canvas.getBoundingClientRect();
        this.mouseX = e.clientX - rect.left;
        this.mouseY = e.clientY - rect.top;
    }
    
    selectTowerType(e) {
        const towerType = e.currentTarget.dataset.tower;
        const cost = parseInt(e.currentTarget.dataset.cost);
        
        if (this.coins >= cost) {
            this.selectedTowerType = towerType;
            this.selectedTower = null;
            
            // 更新按钮状态
            document.querySelectorAll('.tower-btn').forEach(btn => {
                btn.classList.remove('selected');
            });
            e.currentTarget.classList.add('selected');
        }
    }
    
    placeTower(x, y) {
        const gridX = Math.floor(x / GAME_CONFIG.GRID_SIZE) * GAME_CONFIG.GRID_SIZE;
        const gridY = Math.floor(y / GAME_CONFIG.GRID_SIZE) * GAME_CONFIG.GRID_SIZE;
        
        if (Utils.canPlaceTower(x, y, this.towers)) {
            const cost = TOWER_CONFIG[this.selectedTowerType].cost;
            if (this.coins >= cost) {
                const tower = new Tower(gridX, gridY, this.selectedTowerType);
                this.towers.push(tower);
                this.coins -= cost;
                this.selectedTowerType = null;
                
                // 清除按钮选择状态
                document.querySelectorAll('.tower-btn').forEach(btn => {
                    btn.classList.remove('selected');
                });
            }
</augment_code_snippet>