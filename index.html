<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>猛鬼宿舍 - 抖音小游戏</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <div id="gameContainer">
        <div id="gameHeader">
            <div class="stats">
                <div class="stat">
                    <span class="label">金币:</span>
                    <span id="coins">100</span>
                </div>
                <div class="stat">
                    <span class="label">生命:</span>
                    <span id="lives">10</span>
                </div>
                <div class="stat">
                    <span class="label">波次:</span>
                    <span id="wave">1</span>
                </div>
                <div class="stat">
                    <span class="label">分数:</span>
                    <span id="score">0</span>
                </div>
            </div>
        </div>
        
        <canvas id="gameCanvas" width="800" height="600"></canvas>
        
        <div id="gameControls">
            <div class="tower-shop">
                <h3>防御设施</h3>
                <div class="tower-buttons">
                    <button class="tower-btn" data-tower="desk" data-cost="50">
                        <div class="tower-icon">📚</div>
                        <div class="tower-name">书桌</div>
                        <div class="tower-cost">50金币</div>
                    </button>
                    <button class="tower-btn" data-tower="chair" data-cost="30">
                        <div class="tower-icon">🪑</div>
                        <div class="tower-name">椅子</div>
                        <div class="tower-cost">30金币</div>
                    </button>
                    <button class="tower-btn" data-tower="lamp" data-cost="80">
                        <div class="tower-icon">💡</div>
                        <div class="tower-name">台灯</div>
                        <div class="tower-cost">80金币</div>
                    </button>
                    <button class="tower-btn" data-tower="fan" data-cost="120">
                        <div class="tower-icon">🌀</div>
                        <div class="tower-name">风扇</div>
                        <div class="tower-cost">120金币</div>
                    </button>
                </div>
            </div>
            
            <div class="game-actions">
                <button id="startBtn" class="action-btn">开始游戏</button>
                <button id="pauseBtn" class="action-btn">暂停</button>
                <button id="restartBtn" class="action-btn">重新开始</button>
            </div>
        </div>
        
        <div id="gameOver" class="modal hidden">
            <div class="modal-content">
                <h2>游戏结束</h2>
                <p>最终分数: <span id="finalScore">0</span></p>
                <p>存活波次: <span id="finalWave">0</span></p>
                <button id="restartGameBtn" class="action-btn">重新开始</button>
            </div>
        </div>
    </div>

    <script src="js/utils.js"></script>
    <script src="js/entities.js"></script>
    <script src="js/game.js"></script>
</body>
</html>